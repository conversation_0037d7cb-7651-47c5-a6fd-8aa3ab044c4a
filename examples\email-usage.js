// Example usage of the Email API

// Basic email sending example
async function sendWelcomeEmail(userEmail, userName) {
  const emailData = {
    to: userEmail,
    subject: 'Welcome to QR Analytics!',
    html: `
      <h1>Welcome ${userName}!</h1>
      <p>Thank you for joining QR Analytics. We're excited to have you on board!</p>
      <p>You can now start creating and tracking your QR codes.</p>
      <p>Best regards,<br>The QR Analytics Team</p>
    `,
    text: `Welcome ${userName}! Thank you for joining QR Analytics. We're excited to have you on board! You can now start creating and tracking your QR codes. Best regards, The QR Analytics Team`
  };

  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Welcome email sent successfully:', result.data.messageId);
      return true;
    } else {
      console.error('Failed to send welcome email:', result.error);
      return false;
    }
  } catch (error) {
    console.error('Error sending welcome email:', error);
    return false;
  }
}

// QR Code analytics report email
async function sendAnalyticsReport(userEmail, qrCodeName, analyticsData) {
  const emailData = {
    to: userEmail,
    subject: `Analytics Report for ${qrCodeName}`,
    html: `
      <h1>QR Code Analytics Report</h1>
      <h2>${qrCodeName}</h2>
      
      <h3>Summary</h3>
      <ul>
        <li><strong>Total Scans:</strong> ${analyticsData.totalScans}</li>
        <li><strong>Unique Visitors:</strong> ${analyticsData.uniqueVisitors}</li>
        <li><strong>Top Country:</strong> ${analyticsData.topCountry}</li>
        <li><strong>Top Device:</strong> ${analyticsData.topDevice}</li>
      </ul>
      
      <h3>Recent Activity</h3>
      <p>Last scan: ${analyticsData.lastScan}</p>
      
      <p>View detailed analytics in your dashboard.</p>
      
      <p>Best regards,<br>QR Analytics Team</p>
    `,
    text: `QR Code Analytics Report for ${qrCodeName}\n\nSummary:\n- Total Scans: ${analyticsData.totalScans}\n- Unique Visitors: ${analyticsData.uniqueVisitors}\n- Top Country: ${analyticsData.topCountry}\n- Top Device: ${analyticsData.topDevice}\n\nLast scan: ${analyticsData.lastScan}\n\nView detailed analytics in your dashboard.\n\nBest regards,\nQR Analytics Team`
  };

  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Analytics report sent successfully:', result.data.messageId);
      return true;
    } else {
      console.error('Failed to send analytics report:', result.error);
      return false;
    }
  } catch (error) {
    console.error('Error sending analytics report:', error);
    return false;
  }
}

// Password reset email
async function sendPasswordResetEmail(userEmail, resetToken, baseUrl) {
  const resetUrl = `${baseUrl}/reset-password?token=${resetToken}`;
  
  const emailData = {
    to: userEmail,
    subject: 'Password Reset Request',
    html: `
      <h1>Password Reset Request</h1>
      <p>You requested a password reset for your QR Analytics account.</p>
      <p>Click the link below to reset your password:</p>
      <p><a href="${resetUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p>
      <p>If you didn't request this reset, please ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
      <p>Best regards,<br>QR Analytics Team</p>
    `,
    text: `Password Reset Request\n\nYou requested a password reset for your QR Analytics account.\n\nClick the link below to reset your password:\n${resetUrl}\n\nIf you didn't request this reset, please ignore this email.\n\nThis link will expire in 1 hour.\n\nBest regards,\nQR Analytics Team`
  };

  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Password reset email sent successfully:', result.data.messageId);
      return true;
    } else {
      console.error('Failed to send password reset email:', result.error);
      return false;
    }
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return false;
  }
}

// Notification email for admin
async function sendAdminNotification(adminEmail, subject, message, priority = 'normal') {
  const emailData = {
    to: adminEmail,
    subject: `[QR Analytics] ${subject}`,
    html: `
      <h1>Admin Notification</h1>
      <p><strong>Priority:</strong> ${priority.toUpperCase()}</p>
      <p><strong>Message:</strong></p>
      <div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff;">
        ${message}
      </div>
      <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
    `,
    text: `Admin Notification\n\nPriority: ${priority.toUpperCase()}\n\nMessage:\n${message}\n\nTimestamp: ${new Date().toISOString()}`
  };

  try {
    const response = await fetch('/api/send-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(emailData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('Admin notification sent successfully:', result.data.messageId);
      return true;
    } else {
      console.error('Failed to send admin notification:', result.error);
      return false;
    }
  } catch (error) {
    console.error('Error sending admin notification:', error);
    return false;
  }
}

// Export functions for use in other modules
export {
  sendWelcomeEmail,
  sendAnalyticsReport,
  sendPasswordResetEmail,
  sendAdminNotification
};
