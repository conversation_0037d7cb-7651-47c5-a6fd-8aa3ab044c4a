import type { APIRoute } from 'astro';
import { createTransport } from 'nodemailer';
import { createCorsHeaders, createErrorResponse, createSuccessResponse } from '../../utils';

export interface EmailData {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  cc?: string | string[];
  bcc?: string | string[];
  replyTo?: string;
  attachments?: Array<{
    filename: string;
    content: string | Buffer;
    contentType?: string;
  }>;
}

export const sendMail = async (data: EmailData, env: any) => {
  try {
    // Validate required environment variables
    if (!env.ZOHO_MAIL || !env.ZOHO_MAIL_PASSWORD) {
      throw new Error('Email configuration missing: ZOHO_MAIL and ZOHO_MAIL_PASSWORD are required');
    }
console.log(env.ZOHO_MAIL, env.ZOHO_MAIL_PASSWORD);
    let transporter = createTransport({
      host: "smtp.zoho.in",
      secure: true,
      port: 465,
      auth: {
        user: env.Z<PERSON>O_MAIL,
        pass: env.ZOHO_MAIL_PASSWORD,
      },
    });

    const mailOptions = {
      from: env.ZOHO_MAIL,
      ...data,
    };

    // Use promise-based approach instead of callback
    const info = await transporter.sendMail(mailOptions);
    return {
      success: true,
      messageId: info.messageId,
      response: info.response
    };
  } catch (error) {
    console.error('Email sending error:', error);
    throw error;
  }
};

export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;

    if (!env) {
      return createErrorResponse('Environment not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    // Parse request body
    const body = await request.json();
    
    // Validate required fields
    if (!body.to || !body.subject) {
      return createErrorResponse('Missing required fields: to and subject are required', 400, corsHeaders);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const recipients = Array.isArray(body.to) ? body.to : [body.to];
    
    for (const email of recipients) {
      if (!emailRegex.test(email)) {
        return createErrorResponse(`Invalid email format: ${email}`, 400, corsHeaders);
      }
    }

    // Validate that either text or html content is provided
    if (!body.text && !body.html) {
      return createErrorResponse('Email content is required: provide either text or html', 400, corsHeaders);
    }

    // Prepare email data
    const emailData: EmailData = {
      to: body.to,
      subject: body.subject,
      text: body.text,
      html: body.html,
      cc: body.cc,
      bcc: body.bcc,
      replyTo: body.replyTo,
      attachments: body.attachments
    };

    // Send email
    const result = await sendMail(emailData, env);

    return createSuccessResponse(
      {
        message: 'Email sent successfully',
        messageId: result.messageId,
        response: result.response
      },
      200,
      corsHeaders
    );

  } catch (error) {
    console.error('Send email API error:', error);
    
    // Return appropriate error message
    const errorMessage = error instanceof Error ? error.message : 'Failed to send email';
    const statusCode = errorMessage.includes('configuration missing') ? 503 : 500;
    
    return createErrorResponse(errorMessage, statusCode);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
