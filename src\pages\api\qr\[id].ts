import type { APIRoute } from 'astro';
import { getQRCodeById, storeScanAnalytics, ensureAnalyticsTable, getUserEmail } from '../../../database';
import {
  parseUserAgent,
  getGeoLocation,
  getClientIP,
  generateAnalyticsId,
  isValidRedirectUrl,
  createCorsHeaders,
  parseQRCodeData,
  createErrorResponse,
  sendScanNotification
} from '../../../utils';
import type { ScanAnalytics } from '../../../types';

export const GET: APIRoute = async ({ params, request, locals }) => {
  try {
    // @ts-ignore - Astro runtime types
    const env = locals.runtime?.env;
    const db = env?.DB as D1Database;

    if (!db) {
      return createErrorResponse('D1 database not configured', 503);
    }

    const origin = request.headers.get('Origin');
    const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
    const corsHeaders = createCorsHeaders(origin, allowedOrigins);

    const { id } = params;

    if (!id) {
      return createErrorResponse('QR code ID is required', 400, corsHeaders);
    }

    // Get QR code from database by ID
    const qrCode = await getQRCodeById(db, id);

    if (!qrCode) {
      return createErrorResponse('QR code not found', 404, corsHeaders);
    }

    // Parse QR code data to get redirect URL
    let redirectUrl: string;
    try {
      const parsedData = parseQRCodeData(qrCode);
      redirectUrl = parsedData.url;
    } catch (error) {
      console.error('Error parsing QR code data:', error);
      return createErrorResponse('Invalid QR code data', 400, corsHeaders);
    }

    // Validate redirect URL
    if (!isValidRedirectUrl(redirectUrl)) {
      return createErrorResponse('Invalid redirect URL', 400, corsHeaders);
    }

    // Collect analytics data
    const now = new Date().toISOString();
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('User-Agent');
    const referrer = request.headers.get('Referer');
    const deviceInfo = parseUserAgent(userAgent);
    const geoLocation = getGeoLocation(request);

    const analyticsData: ScanAnalytics = {
      id: generateAnalyticsId(),
      qr_code_id: qrCode.id,
      scan_time: now,
      ip: clientIP,
      user_agent: userAgent,
      referrer: referrer,
      lat: geoLocation.lat,
      lon: geoLocation.lon,
      city: geoLocation.city,
      country: geoLocation.country,
      device: deviceInfo.device,
      os: deviceInfo.os,
      browser: deviceInfo.browser,
      created_at: now
    };

    // Ensure analytics table exists and store analytics data
    try {
      // First ensure the analytics table exists
      const tableReady = await ensureAnalyticsTable(db);
      if (!tableReady) {
        console.error('Failed to ensure analytics table exists');
      }

      // Store analytics data
      const analyticsResult = await storeScanAnalytics(db, analyticsData);
      if (!analyticsResult) {
        console.error('Failed to store analytics - database operation returned false');
      } else {
        console.log('Analytics stored successfully for QR code:', qrCode.id);
        
        // Send email notification if user has email
        if (qrCode.user_id) {
          const userEmail = await getUserEmail(db, qrCode.user_id);
          if (userEmail) {
            // Send notification asynchronously to not delay the redirect
            sendScanNotification(userEmail, qrCode, analyticsData, env).catch(error => {
              console.error('Failed to send scan notification:', error);
            });
          }
        }
      }
    } catch (error) {
      console.error('Failed to store analytics:', error);
      // Continue with redirect even if analytics fails
    }

    // Perform the redirect
    return Response.redirect(redirectUrl, 302);

  } catch (error) {
    console.error('QR redirect by ID error:', error);
    return createErrorResponse('Internal server error', 500);
  }
};

export const OPTIONS: APIRoute = async ({ request, locals }) => {
  // @ts-ignore - Astro runtime types
  const env = locals.runtime?.env;
  const origin = request.headers.get('Origin');
  const allowedOrigins = env?.ALLOWED_ORIGINS || '*';
  const corsHeaders = createCorsHeaders(origin, allowedOrigins);

  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
};
