/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "qr-redirect-backend-v2",
	"compatibility_date": "2025-06-20",
	"compatibility_flags": [
		"nodejs_compat"
	],
	"pages_build_output_dir": "./dist",
	"observability": {
		"enabled": true
	},
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	"r2_buckets": [
		{
			"binding": "QR_LOGOS",
			"bucket_name": "qranalytica",
		}
	],
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "qranalytica-astro",
			"database_id": "1bbe802e-9ae6-46e0-a8d7-9c26e65aa9e6",
			"migrations_dir": "./database/migrations"
		}
	],

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	"vars": {
		"JWT_SECRET": "qranalytica-new-astro-project",
		"ZOHO_MAIL_PASSWORD": "bDichjPEmfKc",
		"ZOHO_MAIL": "<EMAIL>"
	},
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
