<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Scan Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .quick-test {
            background-color: #28a745;
        }
        .quick-test:hover {
            background-color: #1e7e34;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Scan Notification</h1>
        
        <div class="note">
            <strong>Note:</strong> Make sure to set up your email configuration (ZOHO_MAIL and ZOHO_MAIL_PASSWORD) before testing.
        </div>

        <form id="testForm">
            <div class="form-group">
                <label for="userEmail">User Email (required):</label>
                <input type="email" id="userEmail" name="userEmail" required placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="qrCodeName">QR Code Name:</label>
                <input type="text" id="qrCodeName" name="qrCodeName" placeholder="My Test QR Code">
            </div>

            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" name="city" placeholder="San Francisco">
            </div>

            <div class="form-group">
                <label for="country">Country:</label>
                <input type="text" id="country" name="country" placeholder="United States">
            </div>

            <div class="form-group">
                <label for="device">Device:</label>
                <select id="device" name="device">
                    <option value="Mobile">Mobile</option>
                    <option value="Desktop">Desktop</option>
                    <option value="Tablet">Tablet</option>
                </select>
            </div>

            <div class="form-group">
                <label for="browser">Browser:</label>
                <select id="browser" name="browser">
                    <option value="Chrome">Chrome</option>
                    <option value="Safari">Safari</option>
                    <option value="Firefox">Firefox</option>
                    <option value="Edge">Edge</option>
                </select>
            </div>

            <div class="form-group">
                <label for="os">Operating System:</label>
                <select id="os" name="os">
                    <option value="iOS">iOS</option>
                    <option value="Android">Android</option>
                    <option value="Windows">Windows</option>
                    <option value="macOS">macOS</option>
                    <option value="Linux">Linux</option>
                </select>
            </div>

            <div class="form-group">
                <label for="referrer">Referrer (optional):</label>
                <input type="url" id="referrer" name="referrer" placeholder="https://example.com">
            </div>

            <button type="submit">Send Custom Test Notification</button>
            <button type="button" class="quick-test" onclick="quickTest()">Quick Test (GET)</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // Remove empty values
            Object.keys(data).forEach(key => {
                if (data[key] === '') {
                    delete data[key];
                }
            });

            await sendTestNotification(data);
        });

        async function sendTestNotification(data) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Sending test notification...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/api/test-scan-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        📧 Email sent to: ${result.data.sentTo}<br>
                        📱 QR Code: ${result.data.qrCodeName}<br>
                        <em>Check your email inbox for the notification.</em>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }

        async function quickTest() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '⏳ Sending quick test notification...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/api/test-scan-notification', {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Quick Test Success!</strong><br>
                        📧 Email sent to: ${result.data.sentTo}<br>
                        📱 QR Code: ${result.data.qrCodeName}<br>
                        <em>${result.data.note}</em><br>
                        <em>Check your email inbox for the notification.</em>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ Error:</strong> ${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
            }
        }
    </script>
</body>
</html>
