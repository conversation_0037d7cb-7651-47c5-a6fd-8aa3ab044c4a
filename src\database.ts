import type { QRC<PERSON>, ScanAnalytics } from './types';
import type { D1Database } from '@cloudflare/workers-types';

/**
 * Get QR code by ID from the database
 */
export async function getQRCodeById(db: D1Database, qrCodeId: string): Promise<QRCode | null> {
  try {
    const result = await db
      .prepare('SELECT * FROM qr_codes WHERE id = ?')
      .bind(qrCodeId)
      .first<QRCode>();
    console.log("🚀 ~ getQRCodeById ~ result:", result)
    
    return result || null;
  } catch (error) {
    console.error('Error fetching QR code:', error);
    return null;
  }
}

/**
 * Get QR code by custom slug from the database
 */
export async function getQRCodeBySlug(db: D1Database, slug: string): Promise<QRCode | null> {
  try {
    const result = await db
      .prepare('SELECT * FROM qr_codes WHERE custom_slug = ?')
      .bind(slug)
      .first<QRCode>();
    
    return result || null;
  } catch (error) {
    console.error('Error fetching QR code by slug:', error);
    return null;
  }
}

/**
 * Store scan analytics data in the database
 */
export async function storeScanAnalytics(
  db: D1Database,
  analytics: ScanAnalytics
): Promise<boolean> {
  try {
    console.log('Attempting to store analytics:', {
      id: analytics.id,
      qr_code_id: analytics.qr_code_id,
      scan_time: analytics.scan_time,
      ip: analytics.ip,
      city: analytics.city,
      country: analytics.country
    });

    const result = await db
      .prepare(`
        INSERT INTO qr_code_scan_analytics (
          id, qr_code_id, scan_time, ip, user_agent, referrer,
          lat, lon, city, country, device, os, browser, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)
      .bind(
        analytics.id,
        analytics.qr_code_id,
        analytics.scan_time,
        analytics.ip,
        analytics.user_agent,
        analytics.referrer,
        analytics.lat,
        analytics.lon,
        analytics.city,
        analytics.country,
        analytics.device,
        analytics.os,
        analytics.browser,
        analytics.created_at
      )
      .run();

    console.log('Database insert result:', {
      success: result.success,
      meta: result.meta,
      changes: result.meta?.changes,
      last_row_id: result.meta?.last_row_id
    });

    return result.success;
  } catch (error) {
    console.error('Error storing scan analytics:', error);
    console.error('Analytics data that failed:', analytics);
    return false;
  }
}

/**
 * Get analytics data for a specific QR code (for testing/debugging)
 */
export async function getQRCodeAnalytics(
  db: D1Database, 
  qrCodeId: string, 
  limit: number = 100
): Promise<ScanAnalytics[]> {
  try {
    const result = await db
      .prepare(`
        SELECT * FROM qr_code_scan_analytics 
        WHERE qr_code_id = ? 
        ORDER BY scan_time DESC 
        LIMIT ?
      `)
      .bind(qrCodeId, limit)
      .all<ScanAnalytics>();
    
    return result.results || [];
  } catch (error) {
    console.error('Error fetching QR code analytics:', error);
    return [];
  }
}

/**
 * Check if the analytics table exists and create it if needed
 */
export async function ensureAnalyticsTable(db: D1Database): Promise<boolean> {
  try {
    // Check if table exists
    const tableCheck = await db
      .prepare(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='qr_code_scan_analytics'
      `)
      .first();

    if (!tableCheck) {
      console.log('Analytics table does not exist, creating it...');

      // Create the analytics table
      await db
        .prepare(`
          CREATE TABLE IF NOT EXISTS qr_code_scan_analytics (
            id TEXT PRIMARY KEY,
            qr_code_id TEXT NOT NULL,
            scan_time TEXT NOT NULL,
            ip TEXT,
            user_agent TEXT,
            referrer TEXT,
            lat REAL,
            lon REAL,
            city TEXT,
            country TEXT,
            device TEXT,
            os TEXT,
            browser TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (qr_code_id) REFERENCES qr_codes(id)
          )
        `)
        .run();

      console.log('Analytics table created successfully');
    } else {
      console.log('Analytics table already exists');
    }

    return true;
  } catch (error) {
    console.error('Error ensuring analytics table exists:', error);
    return false;
  }
}

/**
 * Get user email by user ID
 */
export async function getUserEmail(db: D1Database, userId: string): Promise<string | null> {
  try {
    const result = await db
      .prepare('SELECT email FROM users WHERE id = ?')
      .bind(userId)
      .first<{ email: string }>();

    return result?.email || null;
  } catch (error) {
    console.error('Error fetching user email:', error);
    return null;
  }
}

/**
 * Health check for database connection
 */
export async function checkDatabaseHealth(db: D1Database): Promise<boolean> {
  try {
    const result = await db
      .prepare('SELECT 1 as health_check')
      .first();

    return result?.health_check === 1;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}
