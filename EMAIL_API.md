# Email API Documentation

This document describes the email sending functionality for the QR Redirect Backend.

## Overview

The email API allows you to send emails through Zoho Mail SMTP service. It supports both plain text and HTML emails, with optional features like CC, BCC, reply-to, and attachments.

## Environment Variables

Before using the email API, you need to configure the following environment variables:

```bash
Z<PERSON><PERSON>_MAIL=<EMAIL>
ZOHO_MAIL_PASSWORD=your-app-password
```

**Note**: For Zoho Mail, you should use an App Password instead of your regular password for security.

## API Endpoints

### Send Email

**Endpoint**: `POST /api/send-email`

**Request Body**:
```json
{
  "to": "<EMAIL>",
  "subject": "Your email subject",
  "text": "Plain text content (optional if html is provided)",
  "html": "<h1>HTML content</h1> (optional if text is provided)",
  "cc": "<EMAIL>",
  "bcc": "<EMAIL>",
  "replyTo": "<EMAIL>",
  "attachments": [
    {
      "filename": "document.pdf",
      "content": "base64-encoded-content",
      "contentType": "application/pdf"
    }
  ]
}
```

**Required Fields**:
- `to`: Recipient email address (string or array of strings)
- `subject`: Email subject line
- Either `text` or `html` (or both)

**Optional Fields**:
- `cc`: Carbon copy recipients
- `bcc`: Blind carbon copy recipients
- `replyTo`: Reply-to address
- `attachments`: Array of attachment objects

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Email sent successfully",
    "messageId": "<<EMAIL>>",
    "response": "250 2.0.0 OK"
  }
}
```

### Test Email

**Endpoint**: `POST /api/test-email`

Sends a test email to verify the email configuration is working.

**Request Body**: None required

**Response**:
```json
{
  "success": true,
  "data": {
    "message": "Email test completed successfully",
    "emailResult": {
      "message": "Email sent successfully",
      "messageId": "<<EMAIL>>",
      "response": "250 2.0.0 OK"
    }
  }
}
```

## Usage Examples

### Basic Email

```javascript
const response = await fetch('/api/send-email', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    to: '<EMAIL>',
    subject: 'Welcome!',
    text: 'Welcome to our service!'
  })
});

const result = await response.json();
console.log(result);
```

### HTML Email with Multiple Recipients

```javascript
const response = await fetch('/api/send-email', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    to: ['<EMAIL>', '<EMAIL>'],
    subject: 'Newsletter',
    html: `
      <h1>Monthly Newsletter</h1>
      <p>Here's what's new this month...</p>
    `,
    cc: '<EMAIL>'
  })
});
```

### Email with Attachment

```javascript
const response = await fetch('/api/send-email', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    to: '<EMAIL>',
    subject: 'Invoice',
    text: 'Please find your invoice attached.',
    attachments: [
      {
        filename: 'invoice.pdf',
        content: 'base64-encoded-pdf-content',
        contentType: 'application/pdf'
      }
    ]
  })
});
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `400`: Bad Request (missing required fields, invalid email format)
- `500`: Internal Server Error (email sending failed)
- `503`: Service Unavailable (email configuration missing)

Example error response:
```json
{
  "success": false,
  "error": "Missing required fields: to and subject are required"
}
```

## Security Considerations

1. **Environment Variables**: Store email credentials securely as environment variables
2. **Input Validation**: The API validates email formats and required fields
3. **CORS**: Proper CORS headers are included in responses
4. **Rate Limiting**: Consider implementing rate limiting for production use

## Deployment

When deploying to Cloudflare Pages, make sure to set the environment variables in your Cloudflare dashboard:

1. Go to your Pages project
2. Navigate to Settings > Environment variables
3. Add `ZOHO_MAIL` and `ZOHO_MAIL_PASSWORD`

## Testing

Use the test endpoint to verify your email configuration:

```bash
curl -X POST https://your-domain.pages.dev/api/test-email \
  -H "Content-Type: application/json"
```

Remember to update the test email address in `src/pages/api/test-email.ts` before testing.
