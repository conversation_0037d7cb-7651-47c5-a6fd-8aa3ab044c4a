// Example: Test QR Code Scan Notification

async function testScanNotification() {
  const testData = {
    userEmail: '<EMAIL>', // Replace with a real email for testing
    qrCodeName: 'My Marketing Campaign QR',
    city: 'San Francisco',
    country: 'United States',
    device: 'Mobile',
    browser: 'Chrome',
    os: 'Android',
    referrer: 'https://social-media-platform.com'
  };

  try {
    const response = await fetch('/api/test-scan-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Test notification sent successfully!');
      console.log('📧 Sent to:', result.data.sentTo);
      console.log('📱 QR Code:', result.data.qrCodeName);
      console.log('Check your email inbox for the notification.');
    } else {
      console.error('❌ Failed to send test notification:', result.error);
    }
  } catch (error) {
    console.error('❌ Error testing scan notification:', error);
  }
}

// Example: Test with different scenarios
async function testDifferentScenarios() {
  const scenarios = [
    {
      name: 'Mobile Safari from Social Media',
      data: {
        userEmail: '<EMAIL>',
        qrCodeName: 'Restaurant Menu QR',
        city: 'New York',
        country: 'United States',
        device: 'Mobile',
        browser: 'Safari',
        os: 'iOS',
        referrer: 'https://instagram.com'
      }
    },
    {
      name: 'Desktop Chrome Direct Access',
      data: {
        userEmail: '<EMAIL>',
        qrCodeName: 'Event Registration QR',
        city: 'London',
        country: 'United Kingdom',
        device: 'Desktop',
        browser: 'Chrome',
        os: 'Windows',
        referrer: null
      }
    },
    {
      name: 'Tablet from Email',
      data: {
        userEmail: '<EMAIL>',
        qrCodeName: 'Product Information QR',
        city: 'Tokyo',
        country: 'Japan',
        device: 'Tablet',
        browser: 'Firefox',
        os: 'Android',
        referrer: 'https://gmail.com'
      }
    }
  ];

  for (const scenario of scenarios) {
    console.log(`\n🧪 Testing: ${scenario.name}`);
    
    try {
      const response = await fetch('/api/test-scan-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(scenario.data)
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('✅ Success');
      } else {
        console.log('❌ Failed:', result.error);
      }
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
    
    // Wait 1 second between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Example: Simulate real QR code scan
async function simulateQRCodeScan(qrCodeId) {
  console.log(`🔍 Simulating scan of QR code: ${qrCodeId}`);
  
  try {
    // This would be a real QR code scan - just accessing the redirect URL
    const response = await fetch(`/api/qr/${qrCodeId}`, {
      method: 'GET',
      redirect: 'manual' // Don't follow the redirect, just trigger the scan
    });
    
    if (response.status === 302) {
      console.log('✅ QR code scan successful - redirect triggered');
      console.log('📧 Email notification should be sent to the QR code owner');
      console.log('🔗 Redirect URL:', response.headers.get('Location'));
    } else {
      console.log('❌ QR code scan failed:', response.status);
    }
  } catch (error) {
    console.error('❌ Error simulating QR code scan:', error);
  }
}

// Usage examples:

// Test basic notification
// testScanNotification();

// Test multiple scenarios
// testDifferentScenarios();

// Simulate real QR code scan (replace with actual QR code ID)
// simulateQRCodeScan('your-qr-code-id-here');

export {
  testScanNotification,
  testDifferentScenarios,
  simulateQRCodeScan
};
